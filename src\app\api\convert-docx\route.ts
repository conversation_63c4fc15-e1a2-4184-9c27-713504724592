import { NextRequest, NextResponse } from 'next/server';
import mammoth from 'mammoth';

export async function POST(request: NextRequest) {
  try {
    // Read the DOCX file from the request body
    const buffer = await request.arrayBuffer();
    
    if (!buffer || buffer.byteLength === 0) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Convert DOCX to HTML using mammoth
    const result = await mammoth.convertToHtml({ buffer: Buffer.from(buffer) });
    
    // Get the HTML content
    let htmlContent = result.value;
    
    // Create filtered HTML similar to Word's "Web Page, Filtered" export
    const filteredHtml = `<!DOCTYPE html>
<html>
<head>
<style>
  body {
    font-family: 'Times New Roman', serif;
    font-size: 12pt;
    line-height: 1.5;
    margin: 1in;
    color: black;
    background-color: white;
  }
  p {
    margin: 0 0 10pt 0;
  }
  h1, h2, h3, h4, h5, h6 {
    margin: 12pt 0 6pt 0;
    font-weight: bold;
  }
  table {
    border-collapse: collapse;
    width: 100%;
    margin: 10pt 0;
  }
  td, th {
    border: 1px solid #000;
    padding: 4pt 8pt;
    text-align: left;
  }
  th {
    background-color: #f0f0f0;
    font-weight: bold;
  }
  .center {
    text-align: center;
  }
  .justify {
    text-align: justify;
  }
  .bold {
    font-weight: bold;
  }
  .underline {
    text-decoration: underline;
  }
  .italic {
    font-style: italic;
  }
  ul, ol {
    margin: 10pt 0;
    padding-left: 30pt;
  }
  li {
    margin: 3pt 0;
  }
</style>
<meta http-equiv=Content-Type content="text/html; charset=windows-1252">
</head>
<body>
${htmlContent}
</body>
</html>`;

    // Log any conversion messages/warnings
    if (result.messages && result.messages.length > 0) {
      console.log('Mammoth conversion messages:', result.messages);
    }

    return NextResponse.json({
      success: true,
      htmlContent: filteredHtml,
      messages: result.messages || []
    });

  } catch (error) {
    console.error('Error converting DOCX:', error);
    return NextResponse.json(
      { 
        error: 'Failed to convert DOCX file',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
